"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useTranslations } from "next-intl";
import { useMemo } from "react";
import { authClient } from "@/lib/auth/client";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Text from "@/components/Text";
import { cn } from "@/lib/utils";
import { ShopInfoType } from "@/app/[locale]/data";
import { useAutoAnimate } from "@formkit/auto-animate/react";
import { phoneNumberSchema, profileSchema } from "@/app/[locale]/(auth)/model";
import { toast } from "sonner";

const paymentMethods = [
  "bank_transfer",
  "multicaixa_express",
  "pay_in_store",
] as const;

const formSchema = profileSchema
  .pick({
    firstName: true,
    lastName: true,
  })
  .extend({
    phone: phoneNumberSchema,
    email: z.string().email({ message: "Email inválido." }),
    paymentMethod: z.enum(paymentMethods, {
      message: "Selecione um método de pagamento.",
    }),
    total: z.number().positive({ message: "Total deve ser positivo." }),
  });

type CheckoutFormProps = {
  orderReference: string;
  total: number;
  className?: string;
  selectedShop: ShopInfoType | null;
};

const CartCheckoutForm = ({
  total,
  className,
  orderReference,
  selectedShop,
}: CheckoutFormProps) => {
  const t = useTranslations("CheckoutForm");
  const [parent] = useAutoAnimate();

  const { data: session } = authClient.useSession();
  const user = session?.user;

  const defaultValues = useMemo(() => {
    if (!user)
      return {
        firstName: "",
        lastName: "",
        phone: "",
        email: "",
        total,
      };
    return {
      firstName: user.name?.split(" ")[0] || "",
      lastName: user.name?.split(" ")[1] || "",
      phone: user.phoneNumber || "",
      email: user.email || "",
      total,
    };
  }, [user, total]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    const formData = {
      ...values,
      store: selectedShop?.name || "No store selected",
      storeAddress: selectedShop?.address || "",
    };
    toast.success("Pedido submetido com sucesso! (Simulação)");
  };

  const paymentMethodValue = form.watch("paymentMethod");

  const isSubmitDisabled = !selectedShop;

  const paymentDetailsMap = {
    bank_transfer: [
      {
        label: t("bankTransferDetails.entityLabel"),
        value: "Rol dideias Lda",
      },
      {
        label: t("bankTransferDetails.nifLabel"),
        value: "**********",
      },
      {
        label: t("bankTransferDetails.bankNameLabel"),
        value: "Banco Atlântico",
      },
      {
        label: t("bankTransferDetails.accountNumberLabel"),
        value: "**************",
      },
      {
        label: t("bankTransferDetails.ibanLabel"),
        value: "AO06 0055 0000 2600 8437 1018 3",
      },
      { label: t("bankTransferDetails.confirmationNote"), value: undefined },
      { label: t("bankTransferDetails.paymentDeadlineNote"), value: undefined },
    ],

    multicaixa_express: [
      {
        label: t("multicaixaExpressDetails.numberLabel"),
        value: "9XX XXX XXX",
      },
      {
        label: t.rich("multicaixaExpressDetails.instructions", {
          orderReference,
          ref: (chunk) => <b className="font-semibold">{chunk}</b>,
        }),
        value: undefined,
      },
      {
        label: t("multicaixaExpressDetails.paymentDeadlineNote"),
        value: undefined,
      },
    ],
    pay_in_store: [
      { label: t("payInStoreDetails.confirmationNote"), value: undefined },
      { label: t("payInStoreDetails.paymentDeadlineNote"), value: undefined },
    ],
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn(
          "grid grid-cols-1 gap-8 lowercase md:grid-cols-2 md:gap-12",
          className,
        )}
      >
        <section className="flex flex-col gap-4">
          <FormField
            control={form.control}
            name="paymentMethod"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("paymentMethodLabel")}</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger id="checkout">
                      <SelectValue
                        placeholder={t("selectPaymentPlaceholder")}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value={paymentMethods[0]}>
                      {t("paymentMethods.bankTransfer")}
                    </SelectItem>
                    <SelectItem value={paymentMethods[1]} disabled>
                      {t("paymentMethods.multicaixaExpress")}
                    </SelectItem>
                    <SelectItem value={paymentMethods[2]} disabled>
                      {t("paymentMethods.payInStore")}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <article ref={parent} className="flex flex-col gap-2">
            {selectedShop ? (
              <div className="mb-2 border-b pb-2">
                <Text size="sm" className="font-semibold">
                  {t("storeInformation")}
                </Text>
                <span className="flex gap-2">
                  <Text size="sm">{t("storeName")}</Text>
                  <Text as="p" size="sm" className="font-semibold">
                    {selectedShop.name}
                  </Text>
                </span>
                <span className="flex gap-2">
                  <Text size="sm">{t("storeAddress")}</Text>
                  <Text as="p" size="sm" className="font-semibold">
                    {selectedShop.address}
                  </Text>
                </span>
                <span className="flex gap-2">
                  <Text size="sm">{t("storePhone")}</Text>
                  <Text as="p" size="sm" className="font-semibold">
                    {selectedShop.phone}
                  </Text>
                </span>
              </div>
            ) : (
              <div className="mb-2 border-b pb-2">
                <Text size="sm" className="text-secondary font-semibold">
                  {t("selectStoreFirst")}
                </Text>
              </div>
            )}
            {paymentMethodValue &&
              paymentDetailsMap[paymentMethodValue].map((detail, index) => (
                <span key={index} className="flex gap-2">
                  <Text size="sm">
                    {detail.label}
                    {detail.value && ":"}
                  </Text>
                  {detail?.value && (
                    <Text
                      as="p"
                      size="sm"
                      className="font-semibold normal-case"
                    >
                      {detail.value}
                    </Text>
                  )}
                </span>
              ))}
          </article>
        </section>

        <section className="flex flex-col gap-8">
          <div className="flex flex-col gap-2 self-end">
            <Text as="h6" className="text-right">
              {t("orderReferenceLabel")}:{" "}
              <b className="font-semibold">{orderReference}</b>
            </Text>
            <Text as="p" size="sm" className="-mb-4 text-right font-semibold">
              {t("requiredFieldsLabel")}
            </Text>
          </div>
          <span className="max-xs:flex-col flex gap-8 *:flex-1 sm:gap-4">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("firstNameLabel")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("firstNamePlaceholder")}
                      maxLength={30}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("lastNameLabel")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("lastNamePlaceholder")}
                      maxLength={30}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </span>
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("phoneLabel")}</FormLabel>
                <FormControl>
                  <Input
                    type="tel"
                    placeholder={t("phonePlaceholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("emailLabel")}</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder={t("emailPlaceholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button
            type="submit"
            className={cn("w-full", isSubmitDisabled && "bg-muted opacity-70")}
            disabled={isSubmitDisabled}
            title={t("payNowButton")}
          >
            {t("payNowButton")}
          </Button>
        </section>
      </form>
    </Form>
  );
};

export default CartCheckoutForm;
